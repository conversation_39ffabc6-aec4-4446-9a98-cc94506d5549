import React, { useState, useMemo } from 'react';
import { Search, Plus, Minus, AlertTriangle, <PERSON>, Star } from 'lucide-react';
import type { MenuCategory, MenuItem } from '../../services';

interface MenuDisplayProps {
  serviceId: number;
  categories: MenuCategory[];
  onItemAdd: (item: MenuItem, quantity: number) => void;
  searchTerm?: string;
  loading?: boolean;
}

interface MenuFilters {
  selectedCategory: string;
  showOnlyAvailable: boolean;
  sortBy: 'name' | 'price' | 'popularity';
  priceRange: { min: number; max: number };
}

const MenuItemCard: React.FC<{
  item: MenuItem;
  onAdd: (quantity: number) => void;
}> = ({ item, onAdd }) => {
  const [quantity, setQuantity] = useState(1);
  const isAvailable = item.stock_disponible > 0;

  return (
    <div className={`
      p-4 rounded-lg border transition-all duration-200 hover:shadow-md
      ${isAvailable 
        ? 'bg-white border-gray-200 hover:border-blue-300' 
        : 'bg-gray-50 border-gray-200 opacity-60'
      }
    `}>
      {/* Image du produit */}
      {item.image_url ? (
        <img
          src={item.image_url}
          alt={item.nom}
          className="w-full h-32 object-cover rounded-lg mb-3"
        />
      ) : (
        <div className="w-full h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
          <span className="text-gray-400 text-sm">Pas d'image</span>
        </div>
      )}

      {/* Informations du produit */}
      <div className="space-y-2">
        <div className="flex items-start justify-between">
          <h3 className="font-medium text-gray-900 line-clamp-2">{item.nom}</h3>
          {item.popularite && item.popularite > 80 && (
            <Star className="w-4 h-4 text-yellow-500 flex-shrink-0 ml-1" />
          )}
        </div>

        {item.description && (
          <p className="text-sm text-gray-600 line-clamp-2">{item.description}</p>
        )}

        {/* Prix et temps de préparation */}
        <div className="flex items-center justify-between">
          <span className="text-lg font-semibold text-gray-900">
            {item.prix_vente.toFixed(2)} FCFA
          </span>
          {item.temps_preparation && (
            <div className="flex items-center text-xs text-gray-500">
              <Clock className="w-3 h-3 mr-1" />
              <span>{item.temps_preparation}min</span>
            </div>
          )}
        </div>

        {/* Allergènes */}
        {item.allergenes && item.allergenes.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {item.allergenes.map((allergene, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full"
              >
                {allergene}
              </span>
            ))}
          </div>
        )}

        {/* Stock disponible */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm">
            {isAvailable ? (
              <span className="text-green-600">
                Stock: {item.stock_disponible}
              </span>
            ) : (
              <div className="flex items-center text-red-600">
                <AlertTriangle className="w-4 h-4 mr-1" />
                <span>Rupture de stock</span>
              </div>
            )}
          </div>
        </div>

        {/* Contrôles de quantité et ajout */}
        {isAvailable && (
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              >
                <Minus className="w-3 h-3" />
              </button>
              <span className="w-8 text-center font-medium">{quantity}</span>
              <button
                onClick={() => setQuantity(Math.min(item.stock_disponible, quantity + 1))}
                className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              >
                <Plus className="w-3 h-3" />
              </button>
            </div>
            
            <button
              onClick={() => onAdd(quantity)}
              disabled={quantity > item.stock_disponible}
              className="
                px-3 py-1 bg-blue-500 text-white text-sm rounded-lg
                hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed
                transition-colors duration-200
              "
            >
              Ajouter
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export const MenuDisplay: React.FC<MenuDisplayProps> = ({
  serviceId,
  categories,
  onItemAdd,
  searchTerm = '',
  loading = false
}) => {
  const [filters, setFilters] = useState<MenuFilters>({
    selectedCategory: 'all',
    showOnlyAvailable: true,
    sortBy: 'name',
    priceRange: { min: 0, max: 1000 }
  });
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // Extraction de tous les items avec filtrage
  const allItems = useMemo(() => {
    return categories.flatMap(cat => 
      cat.items.map(item => ({ ...item, categoryName: cat.nom }))
    );
  }, [categories]);

  // Calcul des prix min/max pour le filtre
  const priceRange = useMemo(() => {
    if (allItems.length === 0) return { min: 0, max: 100 };
    const prices = allItems.map(item => item.prix_vente);
    return {
      min: Math.floor(Math.min(...prices)),
      max: Math.ceil(Math.max(...prices))
    };
  }, [allItems]);

  // Filtrage et tri des items
  const filteredItems = useMemo(() => {
    let items = allItems;

    // Filtre par recherche
    const searchQuery = localSearchTerm || searchTerm;
    if (searchQuery) {
      items = items.filter(item =>
        item.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.categoryName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filtre par catégorie
    if (filters.selectedCategory !== 'all') {
      items = items.filter(item => item.categoryName === filters.selectedCategory);
    }

    // Filtre par disponibilité
    if (filters.showOnlyAvailable) {
      items = items.filter(item => item.stock_disponible > 0);
    }

    // Filtre par prix
    items = items.filter(item => 
      item.prix_vente >= filters.priceRange.min && 
      item.prix_vente <= filters.priceRange.max
    );

    // Tri
    items.sort((a, b) => {
      switch (filters.sortBy) {
        case 'price':
          return a.prix_vente - b.prix_vente;
        case 'popularity':
          return (b.popularite || 0) - (a.popularite || 0);
        default:
          return a.nom.localeCompare(b.nom);
      }
    });

    return items;
  }, [allItems, localSearchTerm, searchTerm, filters]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-3">
                <div className="h-32 bg-gray-200 rounded-lg"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Menu</h2>
        <span className="text-sm text-gray-500">
          {filteredItems.length} produit{filteredItems.length > 1 ? 's' : ''}
        </span>
      </div>

      {/* Barre de recherche */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Rechercher un produit..."
          value={localSearchTerm}
          onChange={(e) => setLocalSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Filtres */}
      <div className="mb-4 space-y-3">
        {/* Sélection de catégorie */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilters(prev => ({ ...prev, selectedCategory: 'all' }))}
            className={`
              px-3 py-1 text-sm rounded-full transition-colors
              ${filters.selectedCategory === 'all'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }
            `}
          >
            Toutes
          </button>
          {categories.map(category => (
            <button
              key={category.categorie_id}
              onClick={() => setFilters(prev => ({ ...prev, selectedCategory: category.nom }))}
              className={`
                px-3 py-1 text-sm rounded-full transition-colors
                ${filters.selectedCategory === category.nom
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }
              `}
            >
              {category.nom} ({category.items.length})
            </button>
          ))}
        </div>

        {/* Autres filtres */}
        <div className="flex items-center space-x-4">
          <label className="flex items-center text-sm">
            <input
              type="checkbox"
              checked={filters.showOnlyAvailable}
              onChange={(e) => setFilters(prev => ({ ...prev, showOnlyAvailable: e.target.checked }))}
              className="mr-2"
            />
            Disponibles uniquement
          </label>

          <select
            value={filters.sortBy}
            onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="name">Nom</option>
            <option value="price">Prix</option>
            <option value="popularity">Popularité</option>
          </select>
        </div>
      </div>

      {/* Liste des produits */}
      {filteredItems.length === 0 ? (
        <div className="text-center py-8">
          <Search className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">Aucun produit trouvé</p>
          <button
            onClick={() => {
              setLocalSearchTerm('');
              setFilters({
                selectedCategory: 'all',
                showOnlyAvailable: false,
                sortBy: 'name',
                priceRange: { min: 0, max: 1000 }
              });
            }}
            className="text-blue-500 hover:text-blue-600 text-sm mt-2"
          >
            Réinitialiser les filtres
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
          {filteredItems.map((item) => (
            <MenuItemCard
              key={item.produit_id}
              item={item}
              onAdd={(quantity) => onItemAdd(item, quantity)}
            />
          ))}
        </div>
      )}
    </div>
  );
};
