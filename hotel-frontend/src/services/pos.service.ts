import api from './api.config';
import { authService } from './auth.service';

// Types pour les points de vente
export interface PointDeVente {
  pos_id: number;
  complexe_id: number;
  service_id: number;
  nom: string;
  emplacement: string;
  caisse_ouverte: boolean;
  fonds_caisse: number;
  employe_actuel_id?: number;
  statut: 'Actif' | 'Inactif' | 'Maintenance';
  configuration?: Record<string, any>;
  created_at: string;
  updated_at?: string;
  // Champs joints
  service_nom?: string;
  type_service?: string;
  employe_nom?: string;
  employe_prenom?: string;
}

export interface CreatePOSParams {
  complexe_id: number;
  service_id: number;
  nom: string;
  emplacement: string;
  fonds_caisse?: number;
  configuration?: Record<string, any>;
}

export interface UpdatePOSParams {
  nom?: string;
  emplacement?: string;
  statut?: 'Actif' | 'Inactif' | 'Maintenance';
  fonds_caisse?: number;
  employe_actuel_id?: number;
  configuration?: Record<string, any>;
}

export interface POSResponse {
  success: boolean;
  data: PointDeVente | PointDeVente[];
  message?: string;
}

export class POSService {
  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    // Si l'utilisateur est un admin de chaîne, on utilise le complexe_id sélectionné
    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        throw new Error('Aucun complexe sélectionné');
      }
      return parseInt(selectedComplexeId, 10);
    }

    // Pour les autres rôles, on utilise le complexe_id de l'utilisateur
    if (!user.complexe_id) {
      throw new Error('Complexe ID non disponible');
    }
    return user.complexe_id;
  }

  // Récupération de tous les points de vente d'un complexe
  async getAllPOS(): Promise<PointDeVente[]> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.get<POSResponse>('/pos', { 
      params: { complexeId }
    });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Récupération d'un point de vente par son ID
  async getPOSById(id: number): Promise<PointDeVente> {
    this.checkAuth();
    const response = await api.get<POSResponse>(`/pos/${id}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Point de vente non trouvé');
  }

  // Récupération des points de vente par service
  async getPOSByService(serviceId: number): Promise<PointDeVente[]> {
    this.checkAuth();
    
    const response = await api.get<POSResponse>(`/pos/service/${serviceId}`);
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Création d'un nouveau point de vente
  async createPOS(data: CreatePOSParams): Promise<PointDeVente> {
    this.checkAuth();
    const complexeId = this.getComplexeId();

    // Utiliser l'ID du complexe récupéré automatiquement
    const posData = {
      ...data,
      complexe_id: complexeId
    };

    const response = await api.post<POSResponse>('/pos', posData);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création du point de vente');
  }

  // Mise à jour d'un point de vente
  async updatePOS(id: number, data: UpdatePOSParams): Promise<PointDeVente> {
    this.checkAuth();
    const response = await api.put<POSResponse>(`/pos/${id}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour du point de vente');
  }

  // Suppression d'un point de vente
  async deletePOS(id: number): Promise<void> {
    this.checkAuth();
    const response = await api.delete<POSResponse>(`/pos/${id}`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression du point de vente');
    }
  }

  // Création automatique d'un point de vente pour un service
  async createPOSForService(serviceId: number): Promise<PointDeVente> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.post<POSResponse>(`/pos/auto/${serviceId}`, {
      complexeId
    });
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création automatique du point de vente');
  }

  // Vérifier si un service a déjà un point de vente
  async hasServicePOS(serviceId: number): Promise<boolean> {
    try {
      const posList = await this.getPOSByService(serviceId);
      return posList.length > 0;
    } catch (error) {
      return false;
    }
  }

  // Obtenir ou créer un point de vente pour un service
  async getOrCreatePOSForService(serviceId: number): Promise<PointDeVente> {
    try {
      const posList = await this.getPOSByService(serviceId);

      if (posList.length > 0) {
        return posList[0]; // Retourner le premier POS trouvé
      }

      // Créer automatiquement un POS si aucun n'existe
      return await this.createPOSForService(serviceId);
    } catch (error) {
      throw new Error('Erreur lors de la récupération ou création du point de vente');
    }
  }

  // Transférer des fonds entre POS
  async transferFunds(fromPosId: number, toPosId: number, montant: number, notes?: string): Promise<any> {
    this.checkAuth();

    const response = await api.post('/pos/transfer', {
      fromPosId,
      toPosId,
      montant,
      notes
    });

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors du transfert de fonds');
  }

  // Retirer des fonds d'un POS
  async withdrawFunds(posId: number, montant: number, notes?: string): Promise<any> {
    this.checkAuth();

    const response = await api.post(`/pos/${posId}/withdraw`, {
      montant,
      notes
    });

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors du retrait de fonds');
  }

  // Ajouter des fonds à un POS
  async addFunds(posId: number, montant: number, notes?: string): Promise<any> {
    this.checkAuth();

    const response = await api.post(`/pos/${posId}/add-funds`, {
      montant,
      notes
    });

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de l\'ajout de fonds');
  }
}

export const posService = new POSService();
